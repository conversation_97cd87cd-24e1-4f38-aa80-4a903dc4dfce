package com.snct.typhoon.test;

import com.snct.common.enums.TyphoonWarningLevel;
import com.snct.dctcore.commoncore.constants.RedisParameter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.geo.Circle;
import org.springframework.data.geo.Distance;
import org.springframework.data.geo.GeoResult;
import org.springframework.data.geo.GeoResults;
import org.springframework.data.geo.Metrics;
import org.springframework.data.geo.Point;
import org.springframework.data.redis.connection.RedisGeoCommands;
import org.springframework.data.redis.core.GeoOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * 台风预警功能测试类
 * 用于验证Redis GEO API的正确用法
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
@Component
public class TyphoonWarningTest {

    public static void main(String[] args) {
        TyphoonWarningTest test = new TyphoonWarningTest();
        test.addTestShipData();
        test.testGeoRadius();
    }

    
    private static final Logger logger = LoggerFactory.getLogger(TyphoonWarningTest.class);
    
    @Autowired
    private RedisTemplate redisTemplate;
    
    /**
     * 测试Redis GEO API
     */
    public void testGeoRadius() {
        try {
            // 测试台风中心坐标（示例：台湾附近）
            double typhoonLng = 121.5;
            double typhoonLat = 25.0;
            Point typhoonCenter = new Point(typhoonLng, typhoonLat);
            
            logger.info("测试台风中心坐标: 经度={}, 纬度={}", typhoonLng, typhoonLat);
            
            // 获取GEO操作对象
            GeoOperations<String, String> geoOps = redisTemplate.opsForGeo();
            
            // 创建距离对象
            Distance maxDistance = new Distance(TyphoonWarningLevel.getMaxRadius(), Metrics.KILOMETERS);

            // 测试GEORADIUS查询
            String geoKey = RedisParameter.SHIP_LOCATION_GEO;
            Circle searchArea = new Circle(typhoonCenter, maxDistance);
            GeoResults<RedisGeoCommands.GeoLocation<String>> results =
                geoOps.radius(
                    geoKey,
                    searchArea,
                    RedisGeoCommands.GeoRadiusCommandArgs.newGeoRadiusArgs()
                        .includeDistance()
                        .includeCoordinates()
                        .sortAscending()
                );
            
            if (results != null && !results.getContent().isEmpty()) {
                logger.info("在{}km范围内发现{}艘船舶", TyphoonWarningLevel.getMaxRadius(), results.getContent().size());
                
                for (GeoResult<RedisGeoCommands.GeoLocation<String>> result : results) {
                    String shipSn = result.getContent().getName();
                    double distance = result.getDistance().getValue();
                    Point shipLocation = result.getContent().getPoint();
                    
                    TyphoonWarningLevel level = TyphoonWarningLevel.determineByDistance(distance);
                    
                    logger.info("船舶: {}, 距离: {}km, 位置: ({}, {}), 预警级别: {}",
                               shipSn, distance, 
                               shipLocation.getX(), shipLocation.getY(),
                               level != null ? level.getDescription() : "无预警");
                }
            } else {
                logger.info("在{}km范围内未发现船舶", TyphoonWarningLevel.getMaxRadius());
            }
            
        } catch (Exception e) {
            logger.error("测试Redis GEO API时发生错误", e);
        }
    }
    
    /**
     * 测试添加测试数据
     */
    public void addTestShipData() {
        try {
            GeoOperations<String, String> geoOps = redisTemplate.opsForGeo();
            String geoKey = RedisParameter.SHIP_LOCATION_GEO;

            // 添加一些测试船舶位置
            geoOps.add(geoKey, new Point(121.0, 24.5), "TEST001");
            geoOps.add(geoKey, new Point(121.2, 24.8), "TEST002");
            geoOps.add(geoKey, new Point(121.8, 25.2), "TEST003");
            
            logger.info("已添加测试船舶数据");
            
        } catch (Exception e) {
            logger.error("添加测试数据时发生错误", e);
        }
    }
    
    /**
     * 清理测试数据
     */
    public void cleanTestData() {
        try {
            GeoOperations<String, String> geoOps = redisTemplate.opsForGeo();
            String geoKey = RedisParameter.SHIP_LOCATION_GEO;

            geoOps.remove(geoKey, "TEST001", "TEST002", "TEST003");
            
            logger.info("已清理测试数据");
            
        } catch (Exception e) {
            logger.error("清理测试数据时发生错误", e);
        }
    }
}
